// ===== SYLLABUS/MATERIALS CODE BACKUP =====
// This file contains all the removed syllabus-related code from admin-panel.js
// Removed on: ${new Date().toISOString()}

// ===== MATERIALS OPERATIONS =====

async loadMaterials() {
    try {
        const snapshot = await this.getCollection('materials');
        const materials = [];

        snapshot.forEach(doc => {
            materials.push({ id: doc.id, ...doc.data() });
        });

        this.renderMaterials(materials);
    } catch (error) {
        console.error('Error loading materials:', error);
        throw error;
    }
}

renderMaterials(materials) {
    // Initialize or update Tabulator table for materials
    if (!this.materialsTable) {
        this.initializeMaterialsTable();
    }

    // Update table data
    this.materialsTable.setData(materials);
}

initializeMaterialsTable() {
    const container = document.getElementById('materialsList');
    if (!container) return;

    // Clear any existing content
    container.innerHTML = '<div id="materialsTable" style="background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); overflow: hidden;"></div>';

    // Initialize Tabulator table
    this.materialsTable = new Tabulator("#materialsTable", {
        height: "400px",
        layout: "fitColumns",
        pagination: "local",
        paginationSize: 10,
        paginationSizeSelector: [5, 10, 20, 50],
        movableColumns: true,
        resizableRows: true,
        selectable: true,
        placeholder: "No materials found. Add your first material above.",
        columns: [
            {
                title: "Course",
                field: "course",
                width: 120,
                sorter: "string",
                headerFilter: "select",
                headerFilterParams: {values: true}
            },
            {
                title: "Subject",
                field: "subjectName",
                width: 150,
                sorter: "string",
                headerFilter: "input"
            },
            {
                title: "Semester",
                field: "semester",
                width: 100,
                sorter: "string",
                headerFilter: "select",
                headerFilterParams: {values: true}
            },
            {
                title: "Type",
                field: "fileType",
                width: 80,
                sorter: "string",
                headerFilter: "select",
                headerFilterParams: {values: true}
            },
            {
                title: "File URL",
                field: "fileUrl",
                width: 200,
                formatter: (cell) => {
                    const url = cell.getValue();
                    return url ? `<a href="${url}" target="_blank" style="color: #3b82f6; text-decoration: none;">${url.length > 50 ? url.substring(0, 50) + '...' : url}</a>` : '';
                }
            },
            {
                title: "Actions",
                field: "actions",
                width: 150,
                formatter: (cell) => {
                    return `
                        <button class="btn btn-sm btn-secondary" onclick="adminPanel.editMaterial('${cell.getRow().getData().id}')" style="margin-right: 5px; padding: 4px 8px; font-size: 12px;">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="adminPanel.deleteMaterial('${cell.getRow().getData().id}')" style="padding: 4px 8px; font-size: 12px;">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    `;
                }
            }
        ]
    });
}

async saveMaterial() {
    try {
        const form = document.getElementById('materialForm');
        const formData = new FormData(form);
        const materialId = formData.get('materialId');

        const materialData = {
            course: formData.get('course'),
            semester: formData.get('semester'),
            subjectName: formData.get('subjectName'),
            fileType: formData.get('fileType'),
            refBooks: formData.get('refBooks') || '',
            fileUrl: formData.get('fileUrl'),
            updatedAt: new Date().toISOString()
        };

        if (materialId) {
            await this.updateDocument('materials', materialId, materialData);
            this.showMessage('Material updated successfully');
        } else {
            materialData.createdAt = new Date().toISOString();
            await this.addDocument('materials', materialData);
            this.showMessage('Material added successfully');
        }

        form.reset();
        document.getElementById('materialId').value = '';
        await this.loadMaterials();
    } catch (error) {
        console.error('Error saving material:', error);
        this.showMessage('Error saving material', 'error');
    }
}

async editMaterial(materialId) {
    try {
        const doc = await this.getDocument('materials', materialId);
        if (doc.exists()) {
            const material = doc.data();

            document.getElementById('materialId').value = materialId;
            document.getElementById('materialCourse').value = material.course || '';
            document.getElementById('materialSemester').value = material.semester || '';
            document.getElementById('subjectName').value = material.subjectName || '';
            document.getElementById('fileType').value = material.fileType || '';
            document.getElementById('refBooks').value = material.refBooks || '';
            document.getElementById('fileUrl').value = material.fileUrl || '';

            document.getElementById('materialForm').scrollIntoView({ behavior: 'smooth' });
        }
    } catch (error) {
        console.error('Error loading material for edit:', error);
        this.showMessage('Error loading material', 'error');
    }
}

async deleteMaterial(materialId) {
    const result = await Swal.fire({
        title: 'Delete Material?',
        text: 'Are you sure you want to delete this material?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!',
        background: '#fff',
        color: '#333'
    });

    if (result.isConfirmed) {
        try {
            await this.deleteDocument('materials', materialId);
            this.showMessage('Material deleted successfully');
            await this.loadMaterials();

            Swal.fire({
                title: 'Deleted!',
                text: 'Material has been deleted successfully.',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false,
                background: '#fff',
                color: '#333'
            });
        } catch (error) {
            console.error('Error deleting material:', error);
            this.showMessage('Error deleting material', 'error');

            Swal.fire({
                title: 'Error!',
                text: 'Failed to delete material. Please try again.',
                icon: 'error',
                background: '#fff',
                color: '#333'
            });
        }
    }
}

// ===== FORM LISTENERS (Material-related) =====
// Material form
document.getElementById('materialForm')?.addEventListener('submit', (e) => {
    e.preventDefault();
    this.saveMaterial();
});

document.getElementById('resetMaterialForm')?.addEventListener('click', () => {
    document.getElementById('materialForm').reset();
    document.getElementById('materialId').value = '';
});

// Add buttons
document.getElementById('addMaterialBtn')?.addEventListener('click', () => {
    document.getElementById('materialForm').reset();
    document.getElementById('materialId').value = '';
});

// ===== PAGE MAPPING (Syllabus entry) =====
// From getCurrentPageFromURL() method:
'syllabus.html': 'syllabus',

// From loadPageData() method:
case 'syllabus':
    await this.loadMaterials();
    if (window.populateCourseDropdowns) await window.populateCourseDropdowns();
    break;

// From pageTitles object:
'syllabus': 'Syllabus & Materials',

// ===== COLLECTIONS REFERENCES =====
// From various methods that include 'materials' in collections array:
const collections = ['courses', 'students', 'faculty', 'notices', 'materials', 'fees'];

// ===== SYLLABUS.HTML CONTENT BACKUP =====
/*
The complete syllabus.html file content was:

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syllabus Management - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
</head>
<body class="light-theme">
    <div class="admin-container">
        <!-- Top Header Bar -->
        <header class="top-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle" aria-label="Toggle Sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <a href="admin-panel.html" class="brand" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 8px;">
                    <span class="brand-icon">🛡️</span>
                    <span class="brand-text">BitBot Admin</span>
                </a>
            </div>

            <div class="header-right">
                <button class="theme-toggle" id="themeToggle" aria-label="Toggle Theme">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="user-menu">
                    <button class="user-avatar" id="userMenuToggle">
                        <i class="fas fa-user-circle"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <div class="user-info">
                            <div class="user-name">Admin User</div>
                            <div class="user-email"><EMAIL></div>
                        </div>
                        <div class="dropdown-divider"></div>
                        <a href="settings.html" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                        <button class="dropdown-item" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Left Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <ul class="sidebar-menu">
                    <li class="menu-item">
                        <a href="admin-panel.html" class="menu-link">
                            <i class="fas fa-chart-pie"></i>
                            <span class="menu-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="courses.html" class="menu-link">
                            <i class="fas fa-book"></i>
                            <span class="menu-text">Courses</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="timetables.html" class="menu-link">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="menu-text">Time Tables</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="syllabus.html" class="menu-link active">
                            <i class="fas fa-file-alt"></i>
                            <span class="menu-text">Syllabus</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="notices.html" class="menu-link">
                            <i class="fas fa-bullhorn"></i>
                            <span class="menu-text">Notices</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="fee-structure.html" class="menu-link">
                            <i class="fas fa-dollar-sign"></i>
                            <span class="menu-text">Fee Structure</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="faculty.html" class="menu-link">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span class="menu-text">Faculty</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="college-info.html" class="menu-link">
                            <i class="fas fa-university"></i>
                            <span class="menu-text">College Info</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="students.html" class="menu-link">
                            <i class="fas fa-user-graduate"></i>
                            <span class="menu-text">Students</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="settings.html" class="menu-link">
                            <i class="fas fa-cog"></i>
                            <span class="menu-text">Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Materials Section -->
            <section id="materials-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title">Syllabus & Materials Management</h1>
                    <div class="section-actions">
                        <button class="btn btn-outline" id="searchMaterials">
                            <i class="fas fa-search"></i>
                            Search
                        </button>
                        <button class="btn btn-primary" id="addMaterialBtn">
                            <i class="fas fa-plus"></i>
                            Add Material
                        </button>
                    </div>
                </div>

                <div class="form-container">
                    <form id="materialForm" class="admin-form">
                        <input type="hidden" id="materialId" name="materialId">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="materialCourse">Course Name *</label>
                                <select id="materialCourse" name="course" required>
                                    <option value="">Select Course</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="materialSemester">Year/Semester *</label>
                                <input type="text" id="materialSemester" name="semester" placeholder="e.g., 1st Year, 2nd Semester" required>
                            </div>

                            <div class="form-group">
                                <label for="subjectName">Subject Name *</label>
                                <input type="text" id="subjectName" name="subjectName" required>
                            </div>

                            <div class="form-group">
                                <label for="fileType">File Type *</label>
                                <select id="fileType" name="fileType" required>
                                    <option value="">Select Type</option>
                                    <option value="Syllabus">Syllabus</option>
                                    <option value="Notes">Notes</option>
                                    <option value="Book">Book</option>
                                </select>
                            </div>

                            <div class="form-group full-width">
                                <label for="refBooks">Reference Books</label>
                                <textarea id="refBooks" name="refBooks" rows="3" placeholder="List reference books"></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="fileUrl">File URL (PDF/DOC) *</label>
                                <input type="url" id="fileUrl" name="fileUrl" placeholder="https://..." required>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Material</button>
                            <button type="button" class="btn btn-secondary" id="resetMaterialForm">Reset</button>
                        </div>
                    </form>
                </div>

                <div class="data-list" id="materialsList">
                    <!-- Materials will be loaded here -->
                </div>
            </section>
        </main>
    </div>

    <!-- Firebase SDK -->
    <script src="js/firebase-config.js"></script>
    <script src="js/firebase-service.js"></script>

    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>

    <script>
        // Initialize the admin panel for syllabus page
        document.addEventListener('DOMContentLoaded', function() {
            // Use singleton pattern to prevent duplicate instances
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'syllabus';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                window.adminPanel.currentPage = 'syllabus';
                console.log('Using existing AdminPanel instance for syllabus');
            }
        });
    </script>
</body>
</html>
*/
