<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faculty Management - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
</head>
<body class="light-theme">
    <div class="admin-container">
        <!-- Top Header Bar -->
        <header class="top-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle" aria-label="Toggle Sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <a href="admin-panel.html" class="brand" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 8px;">
                    <span class="brand-icon">🛡️</span>
                    <span class="brand-text">BitBot Admin</span>
                </a>
            </div>

            <div class="header-right">
                <button class="theme-toggle" id="themeToggle" title="Toggle Dark/Light Mode" aria-label="Toggle Theme">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="user-menu">
                    <button class="user-avatar" id="userMenuToggle" aria-label="User Menu">
                        <i class="fas fa-user-circle"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="settings.html" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>

                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item logout-btn" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Left Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <ul class="sidebar-menu">
                    <li class="menu-item">
                        <a href="admin-panel.html" class="menu-link">
                            <i class="fas fa-chart-pie"></i>
                            <span class="menu-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="courses.html" class="menu-link">
                            <i class="fas fa-book"></i>
                            <span class="menu-text">Courses</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="timetables.html" class="menu-link">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="menu-text">Time Tables</span>
                        </a>
                    </li>

                    <li class="menu-item">
                        <a href="notices.html" class="menu-link">
                            <i class="fas fa-bullhorn"></i>
                            <span class="menu-text">Notices</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="fee-structure.html" class="menu-link">
                            <i class="fas fa-dollar-sign"></i>
                            <span class="menu-text">Fee Structure</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="faculty.html" class="menu-link active">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span class="menu-text">Faculty</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="college-info.html" class="menu-link">
                            <i class="fas fa-university"></i>
                            <span class="menu-text">College Info</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="students.html" class="menu-link">
                            <i class="fas fa-users"></i>
                            <span class="menu-text">Students</span>
                        </a>
                    </li>


                </ul>
            </div>
        </nav>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>



            <!-- Faculty Section -->
            <section id="faculty-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title">Faculty Information Management</h1>
                    <div class="section-actions">
                        <button class="btn btn-outline" id="searchFaculty">
                            <i class="fas fa-search"></i>
                            Search
                        </button>
                        <button class="btn btn-primary" id="addFacultyBtn">
                            <i class="fas fa-plus"></i>
                            Add Faculty Department
                        </button>
                    </div>
                </div>

                <div class="form-container">
                    <form id="facultyForm" class="admin-form">
                        <input type="hidden" id="facultyId" name="facultyId">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="facultyDepartment">Department Name *</label>
                                <input type="text" id="facultyDepartment" name="department" required>
                            </div>

                            <div class="form-group">
                                <label for="facultyHOD">HOD Name *</label>
                                <input type="text" id="facultyHOD" name="hodName" required>
                            </div>
                        </div>

                        <div class="faculty-list-section">
                            <h3>Faculty Members</h3>
                            <div id="facultyMembersList">
                                <div class="faculty-member">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>Name *</label>
                                            <input type="text" name="facultyName[]" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Subject *</label>
                                            <input type="text" name="facultySubject[]" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Email</label>
                                            <input type="email" name="facultyEmail[]">
                                        </div>
                                        <div class="form-group">
                                            <label>Phone</label>
                                            <input type="tel" name="facultyPhone[]">
                                        </div>
                                        <div class="form-group">
                                            <label>Qualification</label>
                                            <input type="text" name="facultyQualification[]">
                                        </div>
                                        <div class="form-group">
                                            <button type="button" class="btn btn-danger remove-faculty">Remove</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-secondary" id="addFacultyMember">+ Add Faculty Member</button>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Faculty Info</button>
                            <button type="button" class="btn btn-secondary" id="resetFacultyForm">Reset</button>
                        </div>
                    </form>
                </div>

                <div class="data-list" id="facultyList">
                    <!-- Faculty departments will be loaded here -->
                </div>
            </section>
        </main>
    </div>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

    <!-- Firebase SDK -->
    <script src="js/firebase-config.js"></script>
    <script src="js/firebase-service.js"></script>

    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>
    
    <script>
        // Initialize the admin panel for faculty page
        document.addEventListener('DOMContentLoaded', function() {
            // Use singleton pattern to prevent duplicate instances
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'faculty';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                window.adminPanel.currentPage = 'faculty';
                console.log('Using existing AdminPanel instance for faculty');
            }
        });
    </script>
</body>
</html>
