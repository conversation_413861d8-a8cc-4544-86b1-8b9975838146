<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Courses - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
    <link rel="stylesheet" href="css/courses.css">
</head>
<body class="light-theme">
    <div class="admin-container">
        <!-- Top Header Bar -->
        <header class="top-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle" aria-label="Toggle Sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <a href="admin-panel.html" class="brand" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 8px;">
                    <span class="brand-icon">🛡️</span>
                    <span class="brand-text">BitBot Admin</span>
                </a>
            </div>

            <div class="header-right">
                <button class="theme-toggle" id="themeToggle" title="Toggle Dark/Light Mode" aria-label="Toggle Theme">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="user-menu">
                    <button class="user-avatar" id="userMenuToggle" aria-label="User Menu">
                        <i class="fas fa-user-circle"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="settings.html" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>

                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item logout-btn" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Left Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <ul class="sidebar-menu">
                    <li class="menu-item">
                        <a href="admin-panel.html" class="menu-link">
                            <i class="fas fa-chart-pie"></i>
                            <span class="menu-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="menu-item has-submenu active">
                        <a href="#" class="menu-link">
                            <i class="fas fa-graduation-cap"></i>
                            <span class="menu-text">Courses</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a href="courses.html" class="submenu-link">
                                    <i class="fas fa-plus-circle"></i>
                                    <span class="submenu-text">Add Courses</span>
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a href="all-courses.html" class="submenu-link active">
                                    <i class="fas fa-list-ul"></i>
                                    <span class="submenu-text">View All</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item has-submenu">
                        <a href="#" class="menu-link">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="menu-text">Time Tables</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a href="timetables.html" class="submenu-link">
                                    <i class="fas fa-plus-circle"></i>
                                    <span class="submenu-text">Add Timetables</span>
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a href="view-timetables.html" class="submenu-link">
                                    <i class="fas fa-list-ul"></i>
                                    <span class="submenu-text">View All</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item">
                        <a href="syllabus.html" class="menu-link">
                            <i class="fas fa-file-alt"></i>
                            <span class="menu-text">Syllabus</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="notices.html" class="menu-link">
                            <i class="fas fa-bullhorn"></i>
                            <span class="menu-text">Notices</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="fee-structure.html" class="menu-link">
                            <i class="fas fa-money-bill-wave"></i>
                            <span class="menu-text">Fee Structure</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="faculty.html" class="menu-link">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span class="menu-text">Faculty</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="college-info.html" class="menu-link">
                            <i class="fas fa-university"></i>
                            <span class="menu-text">College Info</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="students.html" class="menu-link">
                            <i class="fas fa-user-graduate"></i>
                            <span class="menu-text">Students</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="settings.html" class="menu-link">
                            <i class="fas fa-cog"></i>
                            <span class="menu-text">Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- All Courses Section -->
            <section id="all-courses-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title course-title">All Courses</h1>
                    <div class="section-actions course-actions">
                        <button class="btn-import-courses" id="importCoursesBtn">
                            <i class="fas fa-upload"></i>
                            Import Courses
                        </button>
                        <button class="btn-export-courses" id="exportCoursesBtn">
                            <i class="fas fa-download"></i>
                            Export Courses
                        </button>
                        <a href="courses.html" class="btn-add-course">
                            <i class="fas fa-plus"></i>
                            Add New Course
                        </a>
                    </div>
                </div>

                <div class="content-grid full-width">
                    <div class="all-courses-container">
                        <div class="all-courses-header">
                            <h3 class="all-courses-title">
                                <i class="fas fa-list"></i>
                                Course Management
                            </h3>
                            <div class="courses-stats">
                                <span class="stat-item">
                                    <i class="fas fa-book"></i>
                                    Total: <span id="totalCoursesCount">0</span>
                                </span>
                            </div>
                        </div>
                        <div class="all-courses-content">
                            <div id="coursesTable"></div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Edit Course Modal -->
    <div id="editCourseModal" class="course-edit-modal" style="display: none;">
        <div class="course-modal-overlay" onclick="courseManager.closeEditModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="editModalTitle">
                    <i class="fas fa-edit"></i>
                    Edit Course
                </h2>
                <button class="modal-close" onclick="courseManager.closeEditModal()" aria-label="Close Modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="editCourseForm" class="modern-form">
                    <input type="hidden" id="editCourseId" name="courseId">

                    <div class="course-form-grid">
                        <!-- Basic Course Information -->
                        <div class="course-form-group">
                            <label class="course-form-label" for="editCourseName">📚 Course Name *</label>
                            <input class="course-form-input" type="text" id="editCourseName" name="courseName" placeholder="e.g., BCA, B.Tech" required minlength="2" maxlength="100">
                        </div>

                        <div class="course-form-group">
                            <label class="course-form-label" for="editDepartment">🏢 Department *</label>
                            <input class="course-form-input" type="text" id="editDepartment" name="department" placeholder="e.g., Computer Applications, Computer Science" required minlength="2" maxlength="100">
                        </div>

                        <div class="course-form-group">
                            <label class="course-form-label" for="editCourseAffiliation">🏛️ Course Affiliation *</label>
                            <input class="course-form-input" type="text" id="editCourseAffiliation" name="courseAffiliation" placeholder="e.g., AKTU, CCSU" required minlength="2" maxlength="50">
                        </div>

                        <div class="course-form-group">
                            <label class="course-form-label" for="editDuration">⏱️ Duration *</label>
                            <input class="course-form-input" type="text" id="editDuration" name="duration" placeholder="e.g., 3 years" required minlength="1" maxlength="20">
                        </div>

                        <div class="course-form-group">
                            <label class="course-form-label" for="editTotalSeats">👥 Total Seats *</label>
                            <input class="course-form-input" type="number" id="editTotalSeats" name="totalSeats" placeholder="e.g., 60" required min="1" max="1000">
                        </div>

                        <div class="course-form-group">
                            <label class="course-form-label" for="editFeeStructure">💰 Fee Structure (₹ per semester) *</label>
                            <input class="course-form-input" type="text" id="editFeeStructure" name="feeStructure" placeholder="e.g., ₹25,000" required minlength="1" maxlength="50">
                        </div>

                        <div class="course-form-group">
                            <label class="course-form-label" for="editOtherFee">➕ Other Fee (Optional)</label>
                            <input class="course-form-input" type="text" id="editOtherFee" name="otherFee" placeholder="e.g., ₹2,000 stationary" maxlength="100">
                        </div>

                        <div class="course-form-group">
                            <label class="course-form-label" for="editHodName">👨‍💼 HOD Name *</label>
                            <input class="course-form-input" type="text" id="editHodName" name="hodName" placeholder="e.g., Dr. Ajay Singh" required minlength="2" maxlength="100">
                        </div>

                        <div class="course-form-group">
                            <label class="course-form-label" for="editCounsellor">👩‍🏫 Course Counsellor</label>
                            <input class="course-form-input" type="text" id="editCounsellor" name="counsellor" placeholder="e.g., Ms. Urvashi Chaudhary" maxlength="100">
                        </div>

                        <!-- Extended Information -->
                        <div class="course-form-group" style="grid-column: 1 / -1;">
                            <label class="course-form-label" for="editScholarshipOpportunities">🎓 Scholarship Opportunities</label>
                            <textarea class="course-form-textarea" id="editScholarshipOpportunities" name="scholarshipOpportunities" rows="3" placeholder="e.g., UP Government Scholarship / Merit-based / None" maxlength="500"></textarea>
                        </div>

                        <div class="course-form-group" style="grid-column: 1 / -1;">
                            <label class="course-form-label" for="editAdmissionEligibility">📋 Admission Eligibility</label>
                            <textarea class="course-form-textarea" id="editAdmissionEligibility" name="admissionEligibility" rows="3" placeholder="e.g., 12th Pass with 45% marks (PCM/Arts/Commerce)" maxlength="500"></textarea>
                        </div>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn-save-course">
                            <i class="fas fa-save"></i>
                            Update Course
                        </button>
                        <button type="button" class="btn-cancel-course" onclick="courseManager.closeEditModal()">
                            <i class="fas fa-times"></i>
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

    <!-- Firebase SDK -->
    <script src="js/firebase-config.js"></script>
    <script src="js/firebase-service.js"></script>

    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>
    <script src="js/courses.js"></script>
    
    <script>
        // Initialize the admin panel for all-courses page
        document.addEventListener('DOMContentLoaded', function() {
            // Use singleton pattern to prevent duplicate instances
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'all-courses';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                window.adminPanel.currentPage = 'all-courses';
                console.log('Using existing AdminPanel instance for all-courses');
            }
        });
    </script>
</body>
</html>
