# Firebase Security Rules Configuration
# Copy these rules to your Firebase Console -> Firestore Database -> Rules

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Admin collection - only authenticated users can read/write their own admin document
    match /admins/{adminId} {
      allow read, write: if request.auth != null && request.auth.uid == adminId;
    }
    
    // Courses collection - authenticated users can read/write
    match /courses/{courseId} {
      allow read, write: if request.auth != null;
    }
    
    // Students collection - authenticated users can read/write
    match /students/{studentId} {
      allow read, write: if request.auth != null;
    }
    
    // Faculty collection - authenticated users can read/write
    match /faculty/{facultyId} {
      allow read, write: if request.auth != null;
    }
    
    // Notices collection - authenticated users can read/write
    match /notices/{noticeId} {
      allow read, write: if request.auth != null;
    }
    
    // Timetables collection - authenticated users can read/write
    match /timetables/{timetableId} {
      allow read, write: if request.auth != null;
    }
    
    // Syllabus/Materials collection - authenticated users can read/write
    match /materials/{materialId} {
      allow read, write: if request.auth != null;
    }
    
    // Fee structure collection - authenticated users can read/write
    match /fees/{feeId} {
      allow read, write: if request.auth != null;
    }
    
    // College info collection - authenticated users can read/write
    match /college-info/{infoId} {
      allow read, write: if request.auth != null;
    }
    
    // Default rule - deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

# FIREBASE SETUP INSTRUCTIONS:

## 1. Firebase Console Setup:

### Step 1: Go to Firebase Console
- Visit: https://console.firebase.google.com/
- Select your project: bitbot-d967b

### Step 2: Enable Authentication
- Go to Authentication -> Sign-in method
- Enable "Email/Password" provider
- Click "Save"

### Step 3: Create Admin User
- Go to Authentication -> Users
- Click "Add user"
- Email: <EMAIL>
- Password: your-secure-password
- Click "Add user"

### Step 4: Configure Firestore Database
- Go to Firestore Database
- If not created, click "Create database"
- Choose "Start in test mode" (we'll update rules later)
- Select your preferred location
- Click "Done"

### Step 5: Update Security Rules
- Go to Firestore Database -> Rules
- Replace the existing rules with the rules provided above
- Click "Publish"

### Step 6: Initialize Collections (Optional)
The admin panel will automatically create collections when needed, but you can manually create them:
- Go to Firestore Database -> Data
- Click "Start collection"
- Create collections: courses, students, faculty, notices, admins

## 2. Admin Panel Configuration:

### Step 1: Update Firebase Config (if needed)
Your current config in firebase-config.js looks correct:
- Project ID: bitbot-d967b
- Auth Domain: bitbot-d967b.firebaseapp.com

### Step 2: Test Admin Login
- Go to your admin panel: admin/admin-panel.html
- Use the email/password you created in Firebase Authentication
- The system will automatically create an admin document in Firestore

## 3. Troubleshooting:

### If you still get "Error loading dashboard data":

1. Check Browser Console:
   - Open Developer Tools (F12)
   - Look for specific error messages
   - Check if Firebase is initialized properly

2. Verify Authentication:
   - Make sure you can log in successfully
   - Check if user appears in Firebase Console -> Authentication

3. Check Firestore Rules:
   - Ensure rules are published correctly
   - Verify authenticated users have read/write access

4. Network Issues:
   - Check if Firebase URLs are accessible
   - Verify internet connection

### Common Error Solutions:

1. "Permission denied" errors:
   - Update Firestore security rules as provided above
   - Ensure user is authenticated before accessing data

2. "Collection not found" errors:
   - The admin panel will now automatically create collections
   - Or manually create them in Firebase Console

3. "Firebase not initialized" errors:
   - Check if firebase-config.js is loaded properly
   - Verify Firebase configuration values

## 4. Testing Steps:

1. Clear browser cache and cookies
2. Go to admin/admin-panel.html
3. Log in with your admin credentials
4. Dashboard should load without errors
5. Check that stat cards show numbers (even if 0)
6. Verify collections are created in Firestore Console

## 5. Production Security (Important):

For production use, consider these additional security measures:

1. Restrict rules to specific admin emails:
```
allow read, write: if request.auth != null && 
  request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
```

2. Add field validation:
```
allow write: if request.auth != null && 
  validateCourseData(request.resource.data);
```

3. Enable App Check for additional security
4. Use Firebase Admin SDK for server-side operations
5. Implement proper user roles and permissions
